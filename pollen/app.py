import streamlit as st

import streamlit as st
import pandas as pd
import requests
import plotly.express as px
from datetime import datetime, timedelta
import random

# --- Configuration ---
# List of locations to monitor
LOCATIONS = {
    "Olomouc, Czechia": {"lat": 49.5937, "lon": 17.2518, "accuweather_key": "OLOMOUC_AW_KEY"},
    "London, UK": {"lat": 48.8566, "lon": 2.3522, "accuweather_key": "PARIS_AW_KEY"},
    "Raleigh, France": {"lat": 48.8566, "lon": 2.3522, "accuweather_key": "PARIS_AW_KEY"}
}

# API Keys (set these in .streamlit/secrets.toml or Streamlit Cloud secrets)
AMBEE_API_KEY = "ERs8qeHdFB2revJ6oYPak8OEDGtFlcWw5JBV6tnR"
ACCUWEATHER_API_KEY = "********************************"
GOOGLE_POLLEN_API_KEY = "AIzaSyC24uT3V0QRc8TFGhRHiugp7S4icvHHjRo"


st.title("🎈 My new app")
st.write(
    "Let's start building! For help and inspiration, head over to [docs.streamlit.io](https://docs.streamlit.io/)."
)
