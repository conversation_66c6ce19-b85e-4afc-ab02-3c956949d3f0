import streamlit as st
import pandas as pd
import asyncio
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import time
from typing import Dict, List, Optional

# Import pollen providers
from openmeteo import OpenMeteoProvider
from ambee import AmbeeProvider
from accuweather import AccuWeatherProvider
import google as google_provider
from models.pollen import Pollen

# --- Configuration ---
# List of locations to monitor with correct coordinates
LOCATIONS = {
    "Olomouc, Czechia": {"lat": 49.5937, "lon": 17.2518},
    "London, UK": {"lat": 51.5074, "lon": -0.1278},
    "Raleigh, NC, USA": {"lat": 35.7796, "lon": -78.6382},
    "Singapore": {"lat": 1.3521, "lon": 103.8198},
    "Berlin, Germany": {"lat": 52.5200, "lon": 13.4050},
    "Paris, France": {"lat": 48.8566, "lon": 2.3522}
}
DYNAMODB_KEY = "NBdyHMQLQZgKnkQz0qXsh2N6nQltlDeWpQJ6OfQI"
# API Keys (you can set these in .streamlit/secrets.toml or environment variables)
AMBEE_API_KEY = "ERs8qeHdFB2revJ6oYPak8OEDGtFlcWw5JBV6tnR"
ACCUWEATHER_API_KEY = "********************************"
GOOGLE_POLLEN_API_KEY = "AIzaSyC24uT3V0QRc8TFGhRHiugp7S4icvHHjRo"

# Provider names for display
PROVIDER_NAMES = {
    "openmeteo": "OpenMeteo (Free)",
    "ambee": "Ambee",
    "accuweather": "AccuWeather",
    "google": "Google Pollen API"
}

# Initialize session state for data storage
if 'pollen_data' not in st.session_state:
    st.session_state.pollen_data = {}
if 'last_fetch_time' not in st.session_state:
    st.session_state.last_fetch_time = None

async def fetch_pollen_data_for_location(location_name: str, lat: float, lon: float) -> Dict[str, Optional[Pollen]]:
    """Fetch pollen data from all providers for a single location."""
    results = {}

    # OpenMeteo (no API key required)
    try:
        openmeteo = OpenMeteoProvider()
        results['openmeteo'] = await openmeteo.fetch_pollen(lat, lon)
    except Exception as e:
        st.error(f"OpenMeteo error for {location_name}: {e}")
        results['openmeteo'] = None

    # Ambee (requires API key)
    try:
        ambee = AmbeeProvider(AMBEE_API_KEY)
        results['ambee'] = await ambee.fetch_pollen(lat, lon)
    except Exception as e:
        st.error(f"Ambee error for {location_name}: {e}")
        results['ambee'] = None

    # AccuWeather (requires API key)
    try:
        accuweather = AccuWeatherProvider(ACCUWEATHER_API_KEY)
        results['accuweather'] = await accuweather.fetch_pollen(lat, lon)
    except Exception as e:
        st.error(f"AccuWeather error for {location_name}: {e}")
        results['accuweather'] = None

    # Google (requires API key)
    try:
        google = google_provider.GoogleProvider(GOOGLE_POLLEN_API_KEY)
        results['google'] = await google.fetch_pollen(lat, lon)
    except Exception as e:
        st.error(f"Google error for {location_name}: {e}")
        results['google'] = None

    return results

async def fetch_all_pollen_data() -> Dict[str, Dict[str, Optional[Pollen]]]:
    """Fetch pollen data from all providers for all locations."""
    all_data = {}

    for location_name, coords in LOCATIONS.items():
        st.write(f"Fetching data for {location_name}...")
        all_data[location_name] = await fetch_pollen_data_for_location(
            location_name, coords['lat'], coords['lon']
        )

    return all_data

def should_fetch_data() -> bool:
    """Check if we should fetch new data (every hour)."""
    if st.session_state.last_fetch_time is None:
        return True

    time_since_last_fetch = datetime.now() - st.session_state.last_fetch_time
    return time_since_last_fetch > timedelta(hours=1)

def display_pollen_data(location_name: str, provider_data: Dict[str, Optional[Pollen]]):
    """Display pollen data for a single location."""
    st.subheader(f"📍 {location_name}")

    # Create columns for each provider
    cols = st.columns(len(PROVIDER_NAMES))

    for idx, (provider_key, provider_name) in enumerate(PROVIDER_NAMES.items()):
        with cols[idx]:
            st.write(f"**{provider_name}**")

            pollen_data = provider_data.get(provider_key)
            if pollen_data:
                # Display timestamp
                st.write(f"🕒 {pollen_data.timestamp.strftime('%H:%M:%S')}")

                # Display pollen counts
                if pollen_data.tree:
                    st.metric("🌳 Tree", pollen_data.tree.count)
                else:
                    st.metric("🌳 Tree", "N/A")

                if pollen_data.grass:
                    st.metric("🌱 Grass", pollen_data.grass.count)
                else:
                    st.metric("🌱 Grass", "N/A")

                if pollen_data.weed:
                    st.metric("🌿 Weed", pollen_data.weed.count)
                else:
                    st.metric("🌿 Weed", "N/A")
            else:
                st.error("No data available")

    st.divider()

def create_summary_chart(all_data: Dict[str, Dict[str, Optional[Pollen]]]):
    """Create a summary chart showing pollen levels across all locations."""
    chart_data = []

    for location_name, provider_data in all_data.items():
        for provider_key, pollen_data in provider_data.items():
            if pollen_data:
                provider_name = PROVIDER_NAMES[provider_key]

                # Add tree pollen
                if pollen_data.tree:
                    chart_data.append({
                        'Location': location_name,
                        'Provider': provider_name,
                        'Type': 'Tree',
                        'Count': pollen_data.tree.count
                    })

                # Add grass pollen
                if pollen_data.grass:
                    chart_data.append({
                        'Location': location_name,
                        'Provider': provider_name,
                        'Type': 'Grass',
                        'Count': pollen_data.grass.count
                    })

                # Add weed pollen
                if pollen_data.weed:
                    chart_data.append({
                        'Location': location_name,
                        'Provider': provider_name,
                        'Type': 'Weed',
                        'Count': pollen_data.weed.count
                    })

    if chart_data:
        df = pd.DataFrame(chart_data)

        # Create grouped bar chart
        fig = px.bar(
            df,
            x='Location',
            y='Count',
            color='Type',
            facet_col='Provider',
            title='Pollen Levels by Location and Provider',
            labels={'Count': 'Pollen Count', 'Location': 'Location'}
        )
        fig.update_layout(height=500)
        st.plotly_chart(fig, use_container_width=True)
    else:
        st.warning("No data available for chart")

# Main Streamlit App
def main():
    st.set_page_config(
        page_title="🌸 Pollen Data Dashboard",
        page_icon="🌸",
        layout="wide"
    )

    st.title("🌸 Pollen Data Dashboard")
    st.write("Real-time pollen data from multiple providers across different locations")

    # Auto-refresh button and status
    col1, col2, col3 = st.columns([1, 1, 2])

    with col1:
        if st.button("🔄 Refresh Data", type="primary"):
            st.session_state.last_fetch_time = None  # Force refresh

    with col2:
        auto_refresh = st.checkbox("Auto-refresh every hour", value=True)

    with col3:
        if st.session_state.last_fetch_time:
            st.write(f"Last updated: {st.session_state.last_fetch_time.strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            st.write("No data fetched yet")

    # Check if we need to fetch data
    if should_fetch_data() or not st.session_state.pollen_data:
        with st.spinner("Fetching pollen data from all providers..."):
            # Run async function
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                st.session_state.pollen_data = loop.run_until_complete(fetch_all_pollen_data())
                st.session_state.last_fetch_time = datetime.now()
                st.success("Data fetched successfully!")
            except Exception as e:
                st.error(f"Error fetching data: {e}")
            finally:
                loop.close()

    # Display data if available
    if st.session_state.pollen_data:
        # Summary chart
        st.header("📊 Summary Chart")
        create_summary_chart(st.session_state.pollen_data)

        # Detailed data by location
        st.header("📍 Detailed Data by Location")
        for location_name, provider_data in st.session_state.pollen_data.items():
            display_pollen_data(location_name, provider_data)

    # Auto-refresh logic
    if auto_refresh and st.session_state.last_fetch_time:
        time_since_last_fetch = datetime.now() - st.session_state.last_fetch_time
        if time_since_last_fetch > timedelta(hours=1):
            st.rerun()

    # Information about providers
    with st.expander("ℹ️ About the Data Providers"):
        st.write("""
        **OpenMeteo (Free)**: European coverage, no API key required

        **Ambee**: Global coverage, requires API key

        **AccuWeather**: Global coverage, requires API key

        **Google Pollen API**: Global coverage, requires API key

        Data is automatically refreshed every hour. Different providers may have different
        data availability and update frequencies.
        """)

if __name__ == "__main__":
    main()
