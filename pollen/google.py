import datetime
from typing import Optional
from uuid import uuid4
import aiohttp
from models.pollen import Pollen, PollenSpecies, PollenSubspecies, CoordinatesModel


class GoogleProvider:
    """Google Pollen API provider."""

    def __init__(self, api_key: str):
        """Initialize with Google API key.

        Args:
            api_key: Google API key with Pollen API access
        """
        self.api_key = api_key
        self.base_url = "https://climate.googleapis.com/v1"

    async def fetch_pollen(self, lat: float, lon: float) -> Optional[Pollen]:
        """Fetch pollen data for given coordinates.

        Args:
            lat: Latitude
            lon: Longitude

        Returns:
            Pollen model instance or None if data unavailable
        """
        url = f"{self.base_url}/pollen:lookup"
        params = {
            "location.latitude": lat,
            "location.longitude": lon,
            "days": 1,
            "key": self.api_key
        }

        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(url, params=params, timeout=10) as response:
                    response.raise_for_status()
                    data = await response.json()

                    return self._parse_response(data, lat, lon)
            except Exception:
                return None

    def _parse_response(self, data: dict, lat: float, lon: float) -> Optional[Pollen]:
        """Parse Google Pollen API response into Pollen model.

        Args:
            data: Google Pollen API response
            lat: Latitude
            lon: Longitude

        Returns:
            Pollen model instance or None
        """
        if not data or not data.get("dailyInfo"):
            return None

        # Get today's info (first entry in dailyInfo)
        today_info = data["dailyInfo"][0]

        tree_count = 0
        grass_count = 0
        weed_count = 0
        tree_subspecies = []
        weed_subspecies = []

        # Parse pollen type info
        for pollen_type_info in today_info.get("pollenTypeInfo", []):
            p_type = pollen_type_info.get("code")  # e.g., "GRASS", "TREE", "WEED"
            index_val = pollen_type_info.get("indexInfo", {}).get("value", 0)

            if p_type == "TREE":
                tree_count = index_val
            elif p_type == "GRASS":
                grass_count = index_val
            elif p_type == "WEED":
                weed_count = index_val

        # Parse plant info for subspecies details if available
        for plant_info in today_info.get("plantInfo", []):
            plant_code = plant_info.get("code")
            plant_index = plant_info.get("indexInfo", {}).get("value", 0)
            plant_name = plant_info.get("displayName", plant_code)

            if plant_index > 0:
                # Categorize plants into tree or weed subspecies
                # Common tree pollen types
                if plant_code in ["ALDER", "ASH", "BEECH", "BIRCH", "CEDAR", "CYPRESS",
                                 "ELM", "HAZEL", "HICKORY", "JUNIPER", "MAPLE", "MULBERRY",
                                 "OAK", "OLIVE", "PINE", "POPLAR", "SYCAMORE", "WALNUT", "WILLOW"]:
                    tree_subspecies.append(PollenSubspecies(name=plant_name.lower(), count=plant_index))
                # Common weed pollen types
                elif plant_code in ["RAGWEED", "MUGWORT", "PLANTAIN", "NETTLE", "SORREL",
                                   "GOOSEFOOT", "PIGWEED", "DOCK", "SAGEBRUSH"]:
                    weed_subspecies.append(PollenSubspecies(name=plant_name.lower(), count=plant_index))

        # Create pollen species objects
        tree_species = PollenSpecies(
            count=tree_count,
            subspecies=tree_subspecies if tree_subspecies else None
        ) if tree_count > 0 else None

        grass_species = PollenSpecies(
            count=grass_count,
            subspecies=None
        ) if grass_count > 0 else None

        weed_species = PollenSpecies(
            count=weed_count,
            subspecies=weed_subspecies if weed_subspecies else None
        ) if weed_count > 0 else None

        return Pollen(
            id=uuid4(),
            timestamp=datetime.datetime.now(datetime.timezone.utc),
            coordinates=CoordinatesModel(latitude=lat, longitude=lon),
            tree=tree_species,
            grass=grass_species,
            weed=weed_species
        )