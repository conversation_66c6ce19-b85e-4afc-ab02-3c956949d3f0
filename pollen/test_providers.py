#!/usr/bin/env python3
"""Test script for pollen providers."""

import asyncio
import os
from accuweather import <PERSON>ccu<PERSON><PERSON><PERSON>Provider
from ambee import <PERSON><PERSON>Provider
from openmeteo import OpenMeteoProvider
from google import GoogleProvider


async def test_providers():
    """Test all pollen providers."""
    # Test coordinates (Prague, Czech Republic)
    lat, lon = 50.0755, 14.4378
    
    print(f"Testing pollen providers for coordinates: {lat}, {lon}")
    print("=" * 60)
    
    # Test OpenMeteo (no API key required)
    print("\n1. Testing OpenMeteo Provider...")
    openmeteo = OpenMeteoProvider()
    try:
        pollen_data = await openmeteo.fetch_pollen(lat, lon)
        if pollen_data:
            print(f"✓ OpenMeteo data retrieved successfully")
            print(f"  ID: {pollen_data.id}")
            print(f"  Timestamp: {pollen_data.timestamp}")
            print(f"  Coordinates: {pollen_data.coordinates}")
            print(f"  Tree: {pollen_data.tree}")
            print(f"  Grass: {pollen_data.grass}")
            print(f"  Weed: {pollen_data.weed}")
        else:
            print("✗ OpenMeteo: No data returned")
    except Exception as e:
        print(f"✗ OpenMeteo error: {e}")
    
    # Test Ambee (requires API key)
    print("\n2. Testing Ambee Provider...")
    ambee_key = os.getenv("AMBEE_API_KEY")
    if ambee_key:
        ambee = AmbeeProvider(ambee_key)
        try:
            pollen_data = await ambee.fetch_pollen(lat, lon)
            if pollen_data:
                print(f"✓ Ambee data retrieved successfully")
                print(f"  ID: {pollen_data.id}")
                print(f"  Timestamp: {pollen_data.timestamp}")
                print(f"  Coordinates: {pollen_data.coordinates}")
                print(f"  Tree: {pollen_data.tree}")
                print(f"  Grass: {pollen_data.grass}")
                print(f"  Weed: {pollen_data.weed}")
            else:
                print("✗ Ambee: No data returned")
        except Exception as e:
            print(f"✗ Ambee error: {e}")
    else:
        print("⚠ Ambee: Skipped (no API key in AMBEE_API_KEY environment variable)")
    
    # Test AccuWeather (requires API key)
    print("\n3. Testing AccuWeather Provider...")
    accuweather_key = os.getenv("ACCUWEATHER_API_KEY")
    if accuweather_key:
        accuweather = AccuWeatherProvider(accuweather_key)
        try:
            pollen_data = await accuweather.fetch_pollen(lat, lon)
            if pollen_data:
                print(f"✓ AccuWeather data retrieved successfully")
                print(f"  ID: {pollen_data.id}")
                print(f"  Timestamp: {pollen_data.timestamp}")
                print(f"  Coordinates: {pollen_data.coordinates}")
                print(f"  Tree: {pollen_data.tree}")
                print(f"  Grass: {pollen_data.grass}")
                print(f"  Weed: {pollen_data.weed}")
            else:
                print("✗ AccuWeather: No data returned")
        except Exception as e:
            print(f"✗ AccuWeather error: {e}")
    else:
        print("⚠ AccuWeather: Skipped (no API key in ACCUWEATHER_API_KEY environment variable)")

    # Test Google Pollen (requires API key)
    print("\n4. Testing Google Pollen Provider...")
    google_key = os.getenv("GOOGLE_POLLEN_API_KEY")
    if google_key:
        google = GoogleProvider(google_key)
        try:
            pollen_data = await google.fetch_pollen(lat, lon)
            if pollen_data:
                print(f"✓ Google Pollen data retrieved successfully")
                print(f"  ID: {pollen_data.id}")
                print(f"  Timestamp: {pollen_data.timestamp}")
                print(f"  Coordinates: {pollen_data.coordinates}")
                print(f"  Tree: {pollen_data.tree}")
                print(f"  Grass: {pollen_data.grass}")
                print(f"  Weed: {pollen_data.weed}")
            else:
                print("✗ Google Pollen: No data returned")
        except Exception as e:
            print(f"✗ Google Pollen error: {e}")
    else:
        print("⚠ Google Pollen: Skipped (no API key in GOOGLE_POLLEN_API_KEY environment variable)")

    print("\n" + "=" * 60)
    print("Test completed!")


if __name__ == "__main__":
    asyncio.run(test_providers())
