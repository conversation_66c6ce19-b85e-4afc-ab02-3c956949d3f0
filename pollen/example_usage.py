#!/usr/bin/env python3
"""Example usage of pollen providers."""

import asyncio
import os
from accuweather import <PERSON>ccu<PERSON>eatherProvider
from ambee import <PERSON><PERSON><PERSON>rovider
from openmeteo import OpenMeteoProvider
from google import GoogleProvider


async def main():
    """Example usage of pollen providers."""
    
    # Example coordinates (Berlin, Germany)
    lat, lon = 52.5200, 13.4050
    
    print(f"Fetching pollen data for Berlin: {lat}, {lon}")
    print("=" * 50)
    
    # 1. OpenMeteo Provider (no API key required)
    print("\n1. OpenMeteo Provider (Free)")
    openmeteo = OpenMeteoProvider()
    pollen_data = await openmeteo.fetch_pollen(lat, lon)
    
    if pollen_data:
        print(f"✓ Data retrieved at {pollen_data.timestamp}")
        if pollen_data.tree:
            print(f"  Tree pollen: {pollen_data.tree.count} grains/m³")
            if pollen_data.tree.subspecies:
                for subspecies in pollen_data.tree.subspecies:
                    print(f"    - {subspecies.name}: {subspecies.count}")
        if pollen_data.grass:
            print(f"  Grass pollen: {pollen_data.grass.count} grains/m³")
        if pollen_data.weed:
            print(f"  Weed pollen: {pollen_data.weed.count} grains/m³")
            if pollen_data.weed.subspecies:
                for subspecies in pollen_data.weed.subspecies:
                    print(f"    - {subspecies.name}: {subspecies.count}")
    else:
        print("✗ No data available")
    
    # 2. Ambee Provider (requires API key)
    print("\n2. Ambee Provider (Requires API Key)")
    ambee_key = os.getenv("AMBEE_API_KEY")
    if ambee_key:
        ambee = AmbeeProvider(ambee_key)
        pollen_data = await ambee.fetch_pollen(lat, lon)
        
        if pollen_data:
            print(f"✓ Data retrieved at {pollen_data.timestamp}")
            if pollen_data.tree:
                print(f"  Tree pollen: {pollen_data.tree.count}")
            if pollen_data.grass:
                print(f"  Grass pollen: {pollen_data.grass.count}")
            if pollen_data.weed:
                print(f"  Weed pollen: {pollen_data.weed.count}")
        else:
            print("✗ No data available")
    else:
        print("⚠ Set AMBEE_API_KEY environment variable to test")
    
    # 3. AccuWeather Provider (requires API key)
    print("\n3. AccuWeather Provider (Requires API Key)")
    accuweather_key = os.getenv("ACCUWEATHER_API_KEY")
    if accuweather_key:
        accuweather = AccuWeatherProvider(accuweather_key)
        pollen_data = await accuweather.fetch_pollen(lat, lon)
        
        if pollen_data:
            print(f"✓ Data retrieved at {pollen_data.timestamp}")
            if pollen_data.tree:
                print(f"  Tree pollen: {pollen_data.tree.count}")
            if pollen_data.grass:
                print(f"  Grass pollen: {pollen_data.grass.count}")
            if pollen_data.weed:
                print(f"  Weed pollen: {pollen_data.weed.count}")
        else:
            print("✗ No data available")
    else:
        print("⚠ Set ACCUWEATHER_API_KEY environment variable to test")
    
    print("\n" + "=" * 50)
    print("Example completed!")


if __name__ == "__main__":
    asyncio.run(main())
